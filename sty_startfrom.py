import pickle

import numpy as np
import pandas as pd
from tqdm import tqdm
from pyevtk.hl import unstructuredGridToVTK
import vtk
from vtk.util import numpy_support
import dask.dataframe as dd
from dask.diagnostics import ProgressBar
import multiprocessing as mp
from typing import List, Dict, Any
import os

import numpy as np
import pandas as pd
from tqdm import tqdm
import vtk
from vtk.util import numpy_support
import os
import re


def load_and_process_data(filename: str) -> List[Dict[str, Any]]:
    """
    更稳健的文件加载方法，完全按照原始格式解析
    """
    with open(filename, 'r') as f:
        content = f.read()

    # 使用正则表达式精确分割区块
    blocks = re.split(r'\[Name\]\s*', content)[1:]

    regions = []
    offset = 0
    for block in tqdm(blocks, desc="处理区域数据", unit="region"):
        # 使用更安全的方式分割数据段
        data_parts = re.split(r'\[Data\]\s*', block, maxsplit=1)
        if len(data_parts) < 2:
            continue

        name = data_parts[0].strip()
        remaining = data_parts[1]

        # 分割面和数据
        face_parts = re.split(r'\[Faces\]\s*', remaining, maxsplit=1)
        if len(face_parts) < 2:
            continue

        data_section = face_parts[0]
        faces_section = face_parts[1]

        # 处理数据部分
        data_lines = [line.strip() for line in data_section.split('\n') if line.strip()]
        header = [h.strip() for h in data_lines[0].split(',')]

        data = np.array([list(map(float, line.split(','))) for line in data_lines[1:]])
        df = pd.DataFrame(data, columns=header)

        # 处理面数据
        faces = []
        ori_faces = []
        for line in faces_section.split('\n'):
            line = line.strip()
            if line:
                try:
                    ori_face = list(map(int, line.split(',')))
                    face = [(it + offset) for it in ori_face]
                    ori_faces.append(ori_face)
                    faces.append(face)
                except:
                    continue
        offset += len(data)
        regions.append({
            "name": name,
            "data": df,
            "faces": faces,
            "ori_faces": ori_faces,
            "points": df[['X [ m ]', 'Y [ m ]', 'Z [ m ]']].values.astype(np.float32)
        })
    print("数据加载处理完成，共处理了", len(regions), "个区域")
    return regions


def extract_boundary_edges(points, faces):
    """提取入口的真实边界点"""

    edge_dict = {}
    for face in faces:
        n = len(face)
        for j in range(n):
            edge = tuple(sorted((face[j], face[(j+1)%n])))
            edge_dict[edge] = edge_dict.get(edge, 0) + 1

    boundary_edges = [e for e, cnt in edge_dict.items() if cnt == 1]
    return np.array([points[e[0]] for e in boundary_edges] +
                    [points[e] for e in boundary_edges])


def calculate_optimal_propagation(points):
    """根据入口尺寸计算最佳传播距离"""
    bbox_size = np.linalg.norm(points.max(axis=0) - points.min(axis=0))
    return bbox_size * 5  # 5倍特征长度


def unstructured_to_structured_grid(unstructured_grid, nx, ny, nz=None):
    """
    将 vtkUnstructuredGrid 转换为 vtkStructuredGrid。

    参数:
    - unstructured_grid: 输入的 vtkUnstructuredGrid 对象
    - nx, ny, nz: 结构化网格的维度（nx × ny × nz）。对于 2D 网格，nz 可设为 None。

    返回:
    - structured_grid: 转换后的 vtkStructuredGrid 对象
    """
    # 检查输入网格
    if not isinstance(unstructured_grid, vtk.vtkUnstructuredGrid):
        raise ValueError("输入必须是 vtkUnstructuredGrid 对象")

    # 获取点数据
    vtk_points = unstructured_grid.GetPoints()
    global_points = numpy_support.vtk_to_numpy(vtk_points.GetData())
    n_points = global_points.shape[0]

    # 确定维度
    if nz is None:  # 2D 网格
        expected_points = nx * ny
        dims = [nx, ny, 1]
    else:  # 3D 网格
        expected_points = nx * ny * nz
        dims = [nx, ny, nz]

    # 验证点数
    if n_points != expected_points:
        raise ValueError(f"点数 {n_points} 与预期结构化网格点数 {expected_points} 不匹配")

    # 按坐标排序以确保 i,j,k 顺序（x 变化最快，然后 y，最后 z）
    sorted_indices = np.lexsort((global_points[:, 2], global_points[:, 1], global_points[:, 0]))
    sorted_points = global_points[sorted_indices]

    # 创建 vtkStructuredGrid
    structured_grid = vtk.vtkStructuredGrid()
    structured_grid.SetDimensions(dims)

    # 设置点数据
    vtk_points_new = vtk.vtkPoints()
    vtk_points_new.SetData(numpy_support.numpy_to_vtk(sorted_points))
    structured_grid.SetPoints(vtk_points_new)

    # 复制点数据（例如速度矢量）
    point_data = unstructured_grid.GetPointData()
    for i in range(point_data.GetNumberOfArrays()):
        array = point_data.GetArray(i)
        if array:
            array_name = array.GetName()
            np_array = numpy_support.vtk_to_numpy(array)
            # 根据排序索引重新排列数据
            sorted_array = np_array[sorted_indices]
            vtk_array = numpy_support.numpy_to_vtk(sorted_array)
            vtk_array.SetName(array_name)
            vtk_array.SetNumberOfComponents(array.GetNumberOfComponents())
            structured_grid.GetPointData().AddArray(vtk_array)
            if array_name == "Velocity":
                structured_grid.GetPointData().SetActiveVectors(array_name)

    # 输出网格信息
    print(f"结构化网格点数: {structured_grid.GetNumberOfPoints()}")
    print(f"结构化网格单元数: {structured_grid.GetNumberOfCells()}")

    return structured_grid


def resample_to_structured_grid(unstructured_grid, dims, bounds=None):
    """
    使用插值将非规则 vtkUnstructuredGrid 转换为 vtkStructuredGrid。

    参数:
    - unstructured_grid: 输入的 vtkUnstructuredGrid 对象
    - dims: 目标结构化网格的维度 [nx, ny, nz]
    - bounds: 网格的边界 [xmin, xmax, ymin, ymax, zmin, zmax]，若为 None 则自动计算

    返回:
    - structured_grid: 转换后的 vtkStructuredGrid 对象
    """
    # 获取边界
    if bounds is None:
        bounds = unstructured_grid.GetBounds()

    # 创建 vtkResampleToImage 滤波器
    resampler = vtk.vtkResampleToImage()
    resampler.SetInputDataObject(unstructured_grid)
    resampler.SetSamplingDimensions(dims)
    resampler.SetSamplingBounds(bounds)
    resampler.Update()

    # 获取输出的 vtkStructuredGrid
    structured_grid = resampler.GetOutput()

    return structured_grid


def create_vertex_sampled_streamlines(regions: List[Dict[str, Any]], output_dir: str = "vertex_streamlines"):
    """
    按顶点采样生成流速迹线
    使用入口区域的所有顶点作为流线起始点，生成密集的流线效果
    """
    print("正在创建按顶点采样的流线可视化...")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 首先合并所有区域的数据，用于全局流线追踪
    all_points = []
    all_data = []
    all_faces = []
    region_map = {}

    for region in regions:
        region_map[region["name"]] = region
        all_points.append(region["points"])
        all_data.append(region["data"])
        all_faces.extend(region["faces"])

    # 检查必要的区域是否存在
    required_regions = ["inlet1too", "inlet2too", "inlet3too", "inlet4too", "outlettoo"]
    for req in required_regions:
        if req not in region_map:
            raise ValueError(f"关键区域 {req} 缺失，无法完成流线可视化")

    # 创建全局网格
    global_points = np.concatenate(all_points)
    global_df = pd.concat(all_data)

    # 创建VTK点集
    vtk_points = vtk.vtkPoints()
    vtk_points.SetData(numpy_support.numpy_to_vtk(global_points))

    # 创建混合单元网格
    grid = vtk.vtkUnstructuredGrid()
    grid.SetPoints(vtk_points)

    # 分别处理不同类型的单元
    for face in all_faces:
        if len(face) == 3:  # 三角形
            triangle = vtk.vtkTriangle()
            triangle.GetPointIds().SetId(0, face[0])
            triangle.GetPointIds().SetId(1, face[1])
            triangle.GetPointIds().SetId(2, face[2])
            grid.InsertNextCell(triangle.GetCellType(), triangle.GetPointIds())
        elif len(face) == 4:  # 四边形
            quad = vtk.vtkQuad()
            for i in range(4):
                quad.GetPointIds().SetId(i, face[i])
            grid.InsertNextCell(quad.GetCellType(), quad.GetPointIds())

    # 添加矢量数据（速度）
    velocity = global_df[['Velocity u [ m s^-1 ]', 'Velocity v [ m s^-1 ]', 'Velocity w [ m s^-1 ]']].values.astype(np.float32)
    velocity_array = numpy_support.numpy_to_vtk(velocity)
    velocity_array.SetName("Velocity")
    velocity_array.SetNumberOfComponents(3)
    grid.GetPointData().SetVectors(velocity_array)
    grid.GetPointData().SetActiveVectors("Velocity")

    print(f"网格点数: {grid.GetNumberOfPoints()}")
    print(f"网格单元数: {grid.GetNumberOfCells()}")

    # 转为 vtkStructuredGrid
    grid = resample_to_structured_grid(grid, dims=(100, 100, 100))

    # ================= 按顶点采样创建流线 =================
    renderer = vtk.vtkRenderer()
    colors = [
        (1, 0, 0),  # 红色 - inlet1too
        (0, 1, 0),  # 绿色 - inlet2too
        (0, 0, 1),  # 蓝色 - inlet3too
        (1, 1, 0),  # 黄色 - inlet4too
    ]

    inlet_names = ["inlet1too", "inlet2too", "inlet3too", "inlet4too"]
    for i, inlet_name in enumerate(inlet_names):
        if inlet_name not in region_map:
            print(f"警告：未找到入口 {inlet_name}")
            continue

        print(f"正在处理 {inlet_name} 的顶点采样流线...")
        # 使用所有顶点作为种子点（顶点采样）
        inlet_region = region_map[inlet_name]
        inlet_points = inlet_region["points"]

        center = inlet_points.mean(axis=0)

        seeds = vtk.vtkPolyData()
        seed_points = vtk.vtkPoints()
        seed_points.InsertNextPoint(center[0], center[1], center[2])
        seeds.SetPoints(seed_points)

        verts = vtk.vtkCellArray()
        verts.InsertNextCell(1)
        verts.InsertCellPoint(0)
        seeds.SetVerts(verts)

        # 四阶龙格库塔积分器
        integ = vtk.vtkRungeKutta4()

        # 创建流线生成器
        streamer = vtk.vtkStreamTracer()
        streamer.SetIntegrator(integ)
        streamer.SetInputData(grid)
        streamer.SetSourceData(seeds)

        # 设置积分参数
        streamer.SetMaximumPropagation(1000)
        streamer.SetInitialIntegrationStep(0.1)  # 更小的步长以获得更精细的流线
        streamer.SetIntegrationDirectionToForward()

        # 更新并检查结果
        streamer.Update()
        streamlines = streamer.GetOutput()

        print(f"  生成的流线数: {streamlines.GetNumberOfCells()}")
        print(f"  流线点数: {streamlines.GetNumberOfPoints()}")

        # 创建流线mapper和actor
        stream_mapper = vtk.vtkPolyDataMapper()
        stream_mapper.SetInputConnection(streamer.GetOutputPort())
        stream_mapper.ScalarVisibilityOff()

        stream_actor = vtk.vtkActor()
        stream_actor.SetMapper(stream_mapper)
        stream_actor.GetProperty().SetColor(colors[i])
        stream_actor.GetProperty().SetLineWidth(2)
        stream_actor.GetProperty().SetOpacity(0.8)

        # 添加到渲染器
        renderer.AddActor(stream_actor)

        print(f"{inlet_name} 顶点采样流线创建完成")

    # 添加入口和出口可视化
    _add_inlet_outlet_visualization(renderer, region_map, inlet_names, colors)

    # 设置渲染场景并保存
    _setup_and_save_visualization(renderer, grid, output_dir, "vertex_sampled_streamlines", "按顶点采样的流线可视化")


def create_uniform_sampled_streamlines(regions: List[Dict[str, Any]], output_dir: str = "uniform_streamlines"):
    """
    等间距采样生成流速迹线
    在入口区域创建规则网格，按固定间距生成种子点，生成规整的流线效果
    """
    print("正在创建等间距采样的流线可视化...")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 首先合并所有区域的数据，用于全局流线追踪
    all_points = []
    all_data = []
    all_faces = []
    region_map = {}

    for region in regions:
        region_map[region["name"]] = region
        all_points.append(region["points"])
        all_data.append(region["data"])
        all_faces.extend(region["faces"])

    # 检查必要的区域是否存在
    required_regions = ["inlet1too", "inlet2too", "inlet3too", "inlet4too", "outlettoo"]
    for req in required_regions:
        if req not in region_map:
            raise ValueError(f"关键区域 {req} 缺失，无法完成流线可视化")

    # 创建全局网格
    global_points = np.concatenate(all_points)
    global_df = pd.concat(all_data)

    # 创建VTK点集
    vtk_points = vtk.vtkPoints()
    vtk_points.SetData(numpy_support.numpy_to_vtk(global_points))

    # 创建混合单元网格
    grid = vtk.vtkUnstructuredGrid()
    grid.SetPoints(vtk_points)

    # 分别处理不同类型的单元
    for face in all_faces:
        if len(face) == 3:  # 三角形
            triangle = vtk.vtkTriangle()
            triangle.GetPointIds().SetId(0, face[0])
            triangle.GetPointIds().SetId(1, face[1])
            triangle.GetPointIds().SetId(2, face[2])
            grid.InsertNextCell(triangle.GetCellType(), triangle.GetPointIds())
        elif len(face) == 4:  # 四边形
            quad = vtk.vtkQuad()
            for i in range(4):
                quad.GetPointIds().SetId(i, face[i])
            grid.InsertNextCell(quad.GetCellType(), quad.GetPointIds())

    # 添加矢量数据（速度）
    velocity = global_df[['Velocity u [ m s^-1 ]', 'Velocity v [ m s^-1 ]', 'Velocity w [ m s^-1 ]']].values.astype(np.float32)
    velocity_array = numpy_support.numpy_to_vtk(velocity)
    velocity_array.SetName("Velocity")
    velocity_array.SetNumberOfComponents(3)
    grid.GetPointData().SetVectors(velocity_array)
    grid.GetPointData().SetActiveVectors("Velocity")

    print(f"网格点数: {grid.GetNumberOfPoints()}")
    print(f"网格单元数: {grid.GetNumberOfCells()}")

    # ================= 等间距采样创建流线 =================
    renderer = vtk.vtkRenderer()
    colors = [
        (1, 0, 0),  # 红色 - inlet1too
        (0, 1, 0),  # 绿色 - inlet2too
        (0, 0, 1),  # 蓝色 - inlet3too
        (1, 1, 0),  # 黄色 - inlet4too
    ]

    inlet_names = ["inlet1too", "inlet2too", "inlet3too", "inlet4too"]
    for i, inlet_name in enumerate(inlet_names):
        if inlet_name not in region_map:
            print(f"警告：未找到入口 {inlet_name}")
            continue

        print(f"正在处理 {inlet_name} 的等间距采样流线...")
        inlet_region = region_map[inlet_name]
        inlet_points = inlet_region["points"]

        # 计算入口区域的边界
        inlet_bbox_min = inlet_points.min(axis=0)
        inlet_bbox_max = inlet_points.max(axis=0)
        inlet_center = inlet_points.mean(axis=0)

        # 创建等间距的规则网格种子点
        seeds = vtk.vtkPolyData()
        seed_points = vtk.vtkPoints()

        # 确定主要的两个方向（排除流动方向）
        bbox_size = inlet_bbox_max - inlet_bbox_min
        sorted_dims = np.argsort(bbox_size)

        # 使用最大的两个维度创建网格
        dim1, dim2 = sorted_dims[1], sorted_dims[2]  # 排除最小的维度

        # 设置网格密度（可调整）
        grid_density = 8  # 每个方向的网格数

        # 在两个主要方向上创建等间距网格
        coord1_range = np.linspace(inlet_bbox_min[dim1], inlet_bbox_max[dim1], grid_density)
        coord2_range = np.linspace(inlet_bbox_min[dim2], inlet_bbox_max[dim2], grid_density)

        # 生成网格点
        for c1 in coord1_range:
            for c2 in coord2_range:
                # 创建种子点坐标
                seed_point = inlet_center.copy()
                seed_point[dim1] = c1
                seed_point[dim2] = c2
                seed_points.InsertNextPoint(seed_point[0], seed_point[1], seed_point[2])

        seeds.SetPoints(seed_points)

        # 创建种子点的单元
        verts = vtk.vtkCellArray()
        for j in range(seed_points.GetNumberOfPoints()):
            verts.InsertNextCell(1)
            verts.InsertCellPoint(j)
        seeds.SetVerts(verts)

        print(f"  使用 {seed_points.GetNumberOfPoints()} 个等间距网格点作为种子点")
        print(f"  网格密度: {grid_density}x{grid_density}")

        # 四阶龙格库塔积分器
        integ = vtk.vtkRungeKutta4()

        # 创建流线生成器
        streamer = vtk.vtkStreamTracer()
        streamer.SetIntegrator(integ)
        streamer.SetInputData(grid)
        streamer.SetSourceData(seeds)

        # 设置积分参数
        streamer.SetMaximumPropagation(100)
        streamer.SetInitialIntegrationStep(0.1)  # 标准步长
        streamer.SetIntegrationDirectionToForward()

        # 更新并检查结果
        streamer.Update()
        streamlines = streamer.GetOutput()

        print(f"  生成的流线数: {streamlines.GetNumberOfCells()}")
        print(f"  流线点数: {streamlines.GetNumberOfPoints()}")

        # 创建流线mapper和actor
        stream_mapper = vtk.vtkPolyDataMapper()
        stream_mapper.SetInputConnection(streamer.GetOutputPort())
        stream_mapper.ScalarVisibilityOff()

        stream_actor = vtk.vtkActor()
        stream_actor.SetMapper(stream_mapper)
        stream_actor.GetProperty().SetColor(colors[i])
        stream_actor.GetProperty().SetLineWidth(3)
        stream_actor.GetProperty().SetOpacity(0.9)

        # 添加到渲染器
        renderer.AddActor(stream_actor)

        print(f"{inlet_name} 等间距采样流线创建完成")

    # 添加入口和出口可视化
    _add_inlet_outlet_visualization(renderer, region_map, inlet_names, colors)

    # 设置渲染场景并保存
    _setup_and_save_visualization(renderer, grid, output_dir, "uniform_sampled_streamlines", "等间距采样的流线可视化")


def _add_inlet_outlet_visualization(renderer, region_map, inlet_names, colors):
    """添加入口和出口区域的可视化"""
    print("添加入口平面可视化...")
    for i, inlet_name in enumerate(inlet_names):
        if inlet_name not in region_map:
            continue

        inlet_region = region_map[inlet_name]

        # 创建入口表面
        inlet_poly = vtk.vtkPolyData()
        inlet_points_vtk = vtk.vtkPoints()

        # 添加入口点
        for point in inlet_region["points"]:
            inlet_points_vtk.InsertNextPoint(point[0], point[1], point[2])

        inlet_poly.SetPoints(inlet_points_vtk)

        # 添加入口面
        inlet_cells = vtk.vtkCellArray()
        for face in inlet_region["ori_faces"]:
            if len(face) == 3:
                triangle = vtk.vtkTriangle()
                triangle.GetPointIds().SetId(0, face[0])
                triangle.GetPointIds().SetId(1, face[1])
                triangle.GetPointIds().SetId(2, face[2])
                inlet_cells.InsertNextCell(triangle)
            elif len(face) == 4:
                quad = vtk.vtkQuad()
                for j in range(4):
                    quad.GetPointIds().SetId(j, face[j])
                inlet_cells.InsertNextCell(quad)

        inlet_poly.SetPolys(inlet_cells)

        # 创建mapper和actor
        inlet_mapper = vtk.vtkPolyDataMapper()
        inlet_mapper.SetInputData(inlet_poly)

        inlet_actor = vtk.vtkActor()
        inlet_actor.SetMapper(inlet_mapper)
        inlet_actor.GetProperty().SetColor(colors[i])
        inlet_actor.GetProperty().SetOpacity(0.5)  # 半透明

        renderer.AddActor(inlet_actor)

    print("入口平面可视化添加完成")

    # 添加出口区域可视化
    print("添加出口区域可视化...")
    outlet_region = region_map["outlettoo"]

    # 创建出口表面
    outlet_poly = vtk.vtkPolyData()
    outlet_points_vtk = vtk.vtkPoints()

    # 添加出口点
    for point in outlet_region["points"]:
        outlet_points_vtk.InsertNextPoint(point[0], point[1], point[2])

    outlet_poly.SetPoints(outlet_points_vtk)

    outlet_cells = vtk.vtkCellArray()
    for face in outlet_region["ori_faces"]:
        if len(face) == 3:
            triangle = vtk.vtkTriangle()
            triangle.GetPointIds().SetId(0, face[0])
            triangle.GetPointIds().SetId(1, face[1])
            triangle.GetPointIds().SetId(2, face[2])
            outlet_cells.InsertNextCell(triangle)
        elif len(face) == 4:
            quad = vtk.vtkQuad()
            for j in range(4):
                quad.GetPointIds().SetId(j, face[j])
            outlet_cells.InsertNextCell(quad)

    outlet_poly.SetPolys(outlet_cells)

    outlet_mapper = vtk.vtkPolyDataMapper()
    outlet_mapper.SetInputData(outlet_poly)

    outlet_actor = vtk.vtkActor()
    outlet_actor.SetMapper(outlet_mapper)
    outlet_actor.GetProperty().SetColor(0.5, 0.5, 0.5)  # 灰色
    outlet_actor.GetProperty().SetOpacity(0.3)  # 半透明

    renderer.AddActor(outlet_actor)
    print("出口区域可视化添加完成")


def _setup_and_save_visualization(renderer, grid, output_dir, filename_prefix, window_title):
    """设置渲染场景并保存结果"""
    # 设置渲染场景
    renderer.SetBackground(0.1, 0.2, 0.3)
    renderer.ResetCamera()

    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    render_window.SetSize(1200, 900)
    render_window.SetWindowName(window_title)

    render_window_interactor = vtk.vtkRenderWindowInteractor()
    render_window_interactor.SetRenderWindow(render_window)

    # 保存为VTK文件
    vtk_file = os.path.join(output_dir, f"{filename_prefix}.vtk")
    writer = vtk.vtkUnstructuredGridWriter()
    writer.SetFileName(vtk_file)
    writer.SetInputData(grid)
    writer.Write()

    # 保存为PNG图片
    window_to_image = vtk.vtkWindowToImageFilter()
    window_to_image.SetInput(render_window)
    window_to_image.SetScale(1)
    window_to_image.SetInputBufferTypeToRGB()
    window_to_image.ReadFrontBufferOff()
    window_to_image.Update()

    writer = vtk.vtkPNGWriter()
    output_file = os.path.join(output_dir, f"{filename_prefix}.png")
    writer.SetFileName(output_file)
    writer.SetInputConnection(window_to_image.GetOutputPort())
    writer.Write()

    print(f"流线可视化完成！结果已保存在 {os.path.abspath(output_dir)}")
    print("红色: inlet1too, 绿色: inlet2too, 蓝色: inlet3too, 黄色: inlet4too")
    print("灰色半透明区域: outlettoo")

    # 交互式显示
    render_window.Render()
    render_window_interactor.Start()





def create_streamlines_comparison(regions: List[Dict[str, Any]]):
    """
    创建两种采样方式的流线对比
    同时生成按顶点采样和等间距采样的流速迹线
    """
    print("=" * 60)
    print("开始生成按顶点采样的流速迹线...")
    print("=" * 60)
    create_vertex_sampled_streamlines(regions)

    # print("\n" + "=" * 60)
    # print("开始生成等间距采样的流速迹线...")
    # print("=" * 60)
    # create_uniform_sampled_streamlines(regions)
    #
    # print("\n" + "=" * 60)
    # print("两种采样方式的流线可视化已完成！")
    # print("=" * 60)
    # print("结果文件：")
    # print("- 按顶点采样：vertex_streamlines/ 目录")
    # print("- 等间距采样：uniform_streamlines/ 目录")
    # print("=" * 60)


def create_velocity_colored_streamlines(regions: List[Dict[str, Any]], output_dir: str = "velocity_colored_streamlines"):
    """
    创建基于速度大小的彩虹色谱颜色映射流线
    统一的速度颜色映射，不按入口区域分组
    """
    print("正在创建基于速度大小的彩虹色谱流线可视化...")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 首先合并所有区域的数据，用于全局流线追踪
    all_points = []
    all_data = []
    all_faces = []
    region_map = {}

    for region in regions:
        region_map[region["name"]] = region
        all_points.append(region["points"])
        all_data.append(region["data"])
        all_faces.extend(region["faces"])

    # 检查必要的区域是否存在
    required_regions = ["inlet1too", "inlet2too", "inlet3too", "inlet4too", "outlettoo"]
    for req in required_regions:
        if req not in region_map:
            raise ValueError(f"关键区域 {req} 缺失，无法完成流线可视化")

    # 合并所有点和数据
    global_points = np.vstack(all_points)
    global_df = pd.concat(all_data, ignore_index=True)

    print(f"全局数据点数: {len(global_points)}")
    print(f"全局数据记录数: {len(global_df)}")

    # 创建VTK非结构化网格
    grid = vtk.vtkUnstructuredGrid()

    # 添加点
    vtk_points = vtk.vtkPoints()
    vtk_points.SetData(numpy_support.numpy_to_vtk(global_points.astype(np.float32)))
    grid.SetPoints(vtk_points)

    # 添加单元（使用顶点单元）
    cells = vtk.vtkCellArray()
    for i in range(len(global_points)):
        vertex = vtk.vtkVertex()
        vertex.GetPointIds().SetId(0, i)
        cells.InsertNextCell(vertex)
    grid.SetCells(vtk.VTK_VERTEX, cells)

    # 添加矢量数据（速度）
    velocity = global_df[['Velocity u [ m s^-1 ]', 'Velocity v [ m s^-1 ]', 'Velocity w [ m s^-1 ]']].values.astype(np.float32)
    velocity_array = numpy_support.numpy_to_vtk(velocity)
    velocity_array.SetName("Velocity")
    velocity_array.SetNumberOfComponents(3)
    grid.GetPointData().SetVectors(velocity_array)
    grid.GetPointData().SetActiveVectors("Velocity")

    # 计算速度大小（magnitude）
    velocity_magnitude = np.sqrt(np.sum(velocity**2, axis=1)).astype(np.float32)
    magnitude_array = numpy_support.numpy_to_vtk(velocity_magnitude)
    magnitude_array.SetName("VelocityMagnitude")
    grid.GetPointData().AddArray(magnitude_array)
    grid.GetPointData().SetActiveScalars("VelocityMagnitude")

    print(f"速度大小范围: {velocity_magnitude.min():.3f} - {velocity_magnitude.max():.3f} m/s")
    print(f"网格点数: {grid.GetNumberOfPoints()}")
    print(f"网格单元数: {grid.GetNumberOfCells()}")

    # 转为 vtkStructuredGrid
    grid = resample_to_structured_grid(grid, dims=(100, 100, 100))

    # ================= 创建统一的速度颜色映射流线 =================
    renderer = vtk.vtkRenderer()

    # 创建种子点 - 使用所有入口的中心点
    inlet_names = ["inlet1too", "inlet2too", "inlet3too", "inlet4too"]
    all_seed_points = []

    for inlet_name in inlet_names:
        inlet_region = region_map[inlet_name]
        inlet_points = inlet_region["points"]
        center = inlet_points.mean(axis=0)
        all_seed_points.append(center)

    # 创建种子点数据
    seeds = vtk.vtkPolyData()
    seed_points = vtk.vtkPoints()

    for seed_point in all_seed_points:
        seed_points.InsertNextPoint(seed_point[0], seed_point[1], seed_point[2])

    seeds.SetPoints(seed_points)

    # 创建顶点单元
    verts = vtk.vtkCellArray()
    for i in range(len(all_seed_points)):
        verts.InsertNextCell(1)
        verts.InsertCellPoint(i)
    seeds.SetVerts(verts)

    print(f"使用 {len(all_seed_points)} 个种子点（所有入口中心）")

    # 四阶龙格库塔积分器
    integ = vtk.vtkRungeKutta4()

    # 创建流线生成器
    streamer = vtk.vtkStreamTracer()
    streamer.SetIntegrator(integ)
    streamer.SetInputData(grid)
    streamer.SetSourceData(seeds)

    # 设置积分参数
    streamer.SetMaximumPropagation(2000)  # 更长追踪
    streamer.SetInitialIntegrationStep(0.05)
    streamer.SetMinimumIntegrationStep(1e-5)
    streamer.SetMaximumIntegrationStep(0.1)
    streamer.SetTerminalSpeed(1e-6)  # 更低终止速度
    streamer.SetIntegrationDirectionToBoth()  # 双向追踪

    # 更新并检查结果
    streamer.Update()
    streamlines = streamer.GetOutput()

    print(f"生成的流线数: {streamlines.GetNumberOfCells()}")
    print(f"流线点数: {streamlines.GetNumberOfPoints()}")

    # 创建流线mapper和actor - 启用速度颜色映射
    stream_mapper = vtk.vtkPolyDataMapper()
    stream_mapper.SetInputConnection(streamer.GetOutputPort())
    stream_mapper.ScalarVisibilityOn()  # 启用标量可视化
    stream_mapper.SetScalarModeToUsePointData()
    stream_mapper.SelectColorArray("VelocityMagnitude")
    stream_mapper.SetScalarRange(velocity_magnitude.min(), velocity_magnitude.max())

    # 设置彩虹色谱查找表
    lut = vtk.vtkLookupTable()
    lut.SetNumberOfTableValues(256)
    lut.SetHueRange(0.667, 0.0)  # 从蓝色(0.667)到红色(0.0)的色相范围
    lut.SetSaturationRange(1.0, 1.0)  # 饱和度
    lut.SetValueRange(1.0, 1.0)  # 亮度
    lut.SetAlphaRange(1.0, 1.0)  # 透明度
    lut.Build()

    stream_mapper.SetLookupTable(lut)

    stream_actor = vtk.vtkActor()
    stream_actor.SetMapper(stream_mapper)
    stream_actor.GetProperty().SetLineWidth(3)
    stream_actor.GetProperty().SetOpacity(0.9)

    # 添加到渲染器
    renderer.AddActor(stream_actor)

    print("已创建基于速度大小的彩虹色谱流线")

    # 添加颜色条（图例）
    scalar_bar = vtk.vtkScalarBarActor()
    scalar_bar.SetLookupTable(lut)
    scalar_bar.SetTitle("速度大小 (m/s)")
    scalar_bar.SetNumberOfLabels(5)
    scalar_bar.SetPosition(0.85, 0.1)
    scalar_bar.SetWidth(0.1)
    scalar_bar.SetHeight(0.8)
    renderer.AddActor2D(scalar_bar)

    # 添加入口和出口可视化
    _add_inlet_outlet_visualization(renderer, region_map, inlet_names,
                                   [(0.8, 0.8, 0.8)] * 4)  # 使用灰色显示入口

    # 设置渲染场景并保存
    _setup_and_save_visualization(renderer, grid, output_dir, "velocity_colored_streamlines",
                                 "基于速度大小的彩虹色谱流线可视化")


def main():
    """主执行函数"""
    # 加载和处理数据
    cache_file = os.path.join(os.getcwd(), "cache.pkl")
    if os.path.exists(cache_file):
        with open(cache_file, "rb") as f:
            regions = pickle.load(f)
    else:
        regions = load_and_process_data("steady_results.csv")
        with open(cache_file, "wb") as f:
            pickle.dump(regions, f)

    # 生成两种不同采样方式的流线可视化对比
    create_streamlines_comparison(regions)

    # 生成基于速度大小的彩虹色谱流线可视化
    create_velocity_colored_streamlines(regions)


if __name__ == "__main__":
    main()