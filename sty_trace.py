import os
import pickle

import numpy as np
from scipy.interpolate import griddata
import vtk
from vtk.util import numpy_support
import pandas as pd

from data_utils import load_and_process_data
np.random.seed(420)


def sty_trace_regions(regions):
    # 合并所有点
    region_map = {}
    for region in regions:
        region_map[region["name"]] = region

    # 检查必要的outlet相关区域是否存在
    entry_regions = [
        'inlet1too',
        # "latooutlet Side 2",
    ]
    output_regions = [
        "inlet1tola Side 1"
    ]
    wall_regions = [
        "inlet1wall"
    ]
    domains = [
        'inlet1',
    ]

    required_regions = entry_regions + domains
    missing_regions = []
    for req in required_regions:
        if req not in region_map:
            missing_regions.append(req)

    if missing_regions:
        print(f"警告：缺失以下outlet相关区域: {missing_regions}")
        print(f"可用区域: {list(region_map.keys())}")
        return

    # 合并相关区域的数据用于流线追踪
    outlet_regions = required_regions
    all_points = []
    all_data = []
    all_faces = []

    for region_name in outlet_regions:
        if region_name in region_map:
            region = region_map[region_name]
            all_points.append(region["points"])
            all_data.append(region["data"])
            all_faces.extend(region["faces"])

    # 合并数据
    global_points = np.vstack(all_points)
    global_df = pd.concat(all_data, ignore_index=True)
    inlet1too_pts = np.vstack(region_map["inlet1too"]["points"])

    print(f"合并后的outlet区域数据点数: {len(global_points)}")

    # 创建VTK非结构化网格
    grid = vtk.vtkUnstructuredGrid()

    # 添加点
    points_vtk = vtk.vtkPoints()
    for point in global_points:
        points_vtk.InsertNextPoint(point[0], point[1], point[2])
    grid.SetPoints(points_vtk)

    # 添加单元（面）
    for face in all_faces:
        if len(face) >= 3:  # 确保是有效的面
            if len(face) == 3:
                cell = vtk.vtkTriangle()
            elif len(face) == 4:
                cell = vtk.vtkQuad()
            else:
                cell = vtk.vtkPolygon()
                cell.GetPointIds().SetNumberOfIds(len(face))

            for i, point_id in enumerate(face):
                cell.GetPointIds().SetId(i, point_id)
            grid.InsertNextCell(cell.GetCellType(), cell.GetPointIds())

    # 添加速度矢量数据
    velocity_cols = ['Velocity u [ m s^-1 ]', 'Velocity v [ m s^-1 ]',
                     'Velocity w [ m s^-1 ]']
    if all(col in global_df.columns for col in velocity_cols):
        velocity = global_df[velocity_cols].values.astype(np.float32)
        velocity_array = numpy_support.numpy_to_vtk(velocity)
        velocity_array.SetName("Velocity")
        velocity_array.SetNumberOfComponents(3)
        grid.GetPointData().SetVectors(velocity_array)
        grid.GetPointData().SetActiveVectors("Velocity")
        print("已添加速度矢量数据")
    else:
        print("警告：未找到速度数据列")
        return

    pts = global_points
    vel = global_df[velocity_cols].values.astype(np.float32)

    margin = 1e-3  # 1 mm
    xmin, ymin, zmin = pts.min(0) - margin
    xmax, ymax, zmax = pts.max(0) + margin

    # 建立规则体网格
    nx, ny, nz = 50, 50, 50
    x,y,z = np.mgrid[xmin:xmax:nx, ymin:ymax:ny, zmin:zmax:nz]

    dx = (xmax - xmin) / (nx - 1)
    dy = (ymax - ymin) / (ny - 1)
    dz = (zmax - zmin) / (nz - 1)

    pts_grid = np.c_[x.ravel(), y.ravel(), z.ravel()]
    vel_grid = griddata(pts, vel, pts_grid, method='linear')   # 插值
    print(vel_grid[:10])

    img = vtk.vtkImageData()
    img.SetDimensions(nx, ny, nz)
    img.SetOrigin(xmin, ymin, zmin)
    img.SetSpacing(dx, dy, dz)

    # 速度矢量
    arr = numpy_support.numpy_to_vtk(vel_grid)
    arr.SetName('Velocity')
    img.GetPointData().SetVectors(arr)

    # 种子点（入口面内均匀）
    seed = vtk.vtkPolyData()
    seed_pts = vtk.vtkPoints()
    stride = max(1, len(inlet1too_pts) // 10)
    for p in inlet1too_pts[::stride]:
        seed_pts.InsertNextPoint(p)
    seed.SetPoints(seed_pts)

    tracer = vtk.vtkStreamTracer()
    tracer.SetInputData(img)
    tracer.SetSourceData(seed)
    tracer.SetIntegratorTypeToRungeKutta45()
    tracer.SetMaximumPropagation(1000)
    tracer.SetIntegrationDirectionToForward()
    tracer.SetInputArrayToProcess(0, 0, 0, vtk.vtkDataObject.FIELD_ASSOCIATION_POINTS, 'Velocity')
    tracer.Update()

    mapper = vtk.vtkPolyDataMapper()
    mapper.SetInputConnection(tracer.GetOutputPort())

    actor = vtk.vtkActor()
    actor.SetMapper(mapper)

    ren = vtk.vtkRenderer()
    # ================= 添加简化的几何边界可视化 =================
    # 1. 入口平面 "latooutlet Side 2" - 只显示点云
    inlet_poly = vtk.vtkPolyData()
    inlet_points_vtk = vtk.vtkPoints()

    inlet_region = region_map[entry_regions[0]]
    for point in inlet_region["points"]:
        inlet_points_vtk.InsertNextPoint(point[0], point[1], point[2])
    inlet_poly.SetPoints(inlet_points_vtk)

    # 创建顶点单元而不是面单元，避免复杂的面处理
    inlet_verts = vtk.vtkCellArray()
    for i in range(inlet_points_vtk.GetNumberOfPoints()):
        inlet_verts.InsertNextCell(1)
        inlet_verts.InsertCellPoint(i)
    inlet_poly.SetVerts(inlet_verts)

    inlet_mapper = vtk.vtkPolyDataMapper()
    inlet_mapper.SetInputData(inlet_poly)

    inlet_actor = vtk.vtkActor()
    inlet_actor.SetMapper(inlet_mapper)
    inlet_actor.GetProperty().SetColor(0, 0.8, 0.2)  # 亮绿色入口点云
    inlet_actor.GetProperty().SetPointSize(4)
    inlet_actor.GetProperty().SetRepresentationToPoints()
    inlet_actor.GetProperty().SetOpacity(0.8)

    ren.AddActor(inlet_actor)
    print("已添加入口平面点云可视化")
    ren.AddActor(actor)

    win = vtk.vtkRenderWindow()
    win.AddRenderer(ren)

    iren = vtk.vtkRenderWindowInteractor()
    iren.SetRenderWindow(win)
    print('img bounds', img.GetBounds())
    print('first seed', seed.GetPoint(0))
    iren.Start()


def main():
    """主执行函数"""
    # 加载和处理数据
    cache_file = os.path.join(os.getcwd(), "cache_export.pkl")
    if os.path.exists(cache_file):
        with open(cache_file, "rb") as f:
            regions = pickle.load(f)
    else:
        regions = load_and_process_data("steady_results.csv")
        with open(cache_file, "wb") as f:
            pickle.dump(regions, f)

    print([it['name'] for it in regions])

    sty_trace_regions(regions)


if __name__ == '__main__':
    main()