import numpy as np
import pandas as pd
from tqdm import tqdm
from vtk.util import numpy_support
from typing import List, Dict, Any
import re


def load_and_process_data(filename: str) -> List[Dict[str, Any]]:
    """
    更稳健的文件加载方法，完全按照原始格式解析
    """
    with open(filename, 'r') as f:
        content = f.read()

    # 使用正则表达式精确分割区块
    blocks = re.split(r'\[Name\]\s*', content)[1:]

    regions = []
    offset = 0
    for block in tqdm(blocks, desc="处理区域数据", unit="region"):
        # 使用更安全的方式分割数据段
        data_parts = re.split(r'\[Data\]\s*', block, maxsplit=1)
        if len(data_parts) < 2:
            continue

        name = data_parts[0].strip()
        remaining = data_parts[1]

        # 分割面和数据
        face_parts = re.split(r'\[Faces\]\s*', remaining, maxsplit=1)
        if len(face_parts) < 2:
            data_section = face_parts[0]
            faces_section = ''
        else:
            data_section = face_parts[0]
            faces_section = face_parts[1]

        # 处理数据部分
        data_lines = [line.strip() for line in data_section.split('\n')
                      if line.strip()]
        header = [h.strip() for h in data_lines[0].split(',')]

        data = np.array([list(map(float, line.split(',')))
                         for line in data_lines[1:]])
        df = pd.DataFrame(data, columns=header)

        # 处理面数据
        faces = []
        ori_faces = []
        for line in faces_section.split('\n'):
            line = line.strip()
            if line:
                try:
                    ori_face = list(map(int, line.split(',')))
                    face = [(it + offset) for it in ori_face]
                    ori_faces.append(ori_face)
                    faces.append(face)
                except (ValueError, IndexError):
                    continue
        offset += len(data)
        regions.append({
            "name": name,
            "data": df,
            "faces": faces,
            "ori_faces": ori_faces,
            "points": df[['X [ m ]', 'Y [ m ]', 'Z [ m ]']].values.astype(
                np.float32)
        })
    print("数据加载处理完成，共处理了", len(regions), "个区域")
    return regions