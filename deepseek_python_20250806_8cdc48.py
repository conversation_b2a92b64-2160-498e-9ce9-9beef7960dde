import os
import numpy as np
import pandas as pd
from typing import List, Dict, Any
from scipy.spatial import cKDTree
import vtk
from vtk.util import numpy_support
import pickle
import numpy as np
import pandas as pd
from tqdm import tqdm
import vtk
from vtk.util import numpy_support
from typing import List, Dict, Any
import os
import re
from scipy.spatial import cKDTree


def load_and_process_data(filename: str) -> List[Dict[str, Any]]:
    """
    更稳健的文件加载方法，完全按照原始格式解析
    """
    with open(filename, 'r') as f:
        content = f.read()

    # 使用正则表达式精确分割区块
    blocks = re.split(r'\[Name\]\s*', content)[1:]

    regions = []
    offset = 0
    for block in tqdm(blocks, desc="处理区域数据", unit="region"):
        # 使用更安全的方式分割数据段
        data_parts = re.split(r'\[Data\]\s*', block, maxsplit=1)
        if len(data_parts) < 2:
            continue

        name = data_parts[0].strip()
        remaining = data_parts[1]

        # 分割面和数据
        face_parts = re.split(r'\[Faces\]\s*', remaining, maxsplit=1)
        if len(face_parts) < 2:
            continue

        data_section = face_parts[0]
        faces_section = face_parts[1]

        # 处理数据部分
        data_lines = [line.strip() for line in data_section.split('\n')
                      if line.strip()]
        header = [h.strip() for h in data_lines[0].split(',')]

        data = np.array([list(map(float, line.split(',')))
                         for line in data_lines[1:]])
        df = pd.DataFrame(data, columns=header)

        # 处理面数据
        faces = []
        ori_faces = []
        for line in faces_section.split('\n'):
            line = line.strip()
            if line:
                try:
                    ori_face = list(map(int, line.split(',')))
                    face = [(it + offset) for it in ori_face]
                    ori_faces.append(ori_face)
                    faces.append(face)
                except (ValueError, IndexError):
                    continue
        offset += len(data)
        regions.append({
            "name": name,
            "data": df,
            "faces": faces,
            "ori_faces": ori_faces,
            "points": df[['X [ m ]', 'Y [ m ]', 'Z [ m ]']].values.astype(
                np.float32)
        })
    print("数据加载处理完成，共处理了", len(regions), "个区域")
    return regions


def create_outlet_streamlines(regions: List[Dict[str, Any]],
                             output_dir: str = "outlet_streamlines",
                             streamline_density: int = 5,
                             max_streamlines: int = 50) -> None:
    """
    创建outlet子区域的流线可视化（优化版）
    
    参数:
        regions: 包含所有区域数据的字典列表
        output_dir: 输出目录路径
        streamline_density: 流线密度控制参数 (3-10)
        max_streamlines: 最大流线数量
        
    功能:
        - 三维区域：Subdomain outlet
        - 入口平面：latooutlet Side 2
        - 出口平面：outlettoo
        - 区域管道表面：outlet Default
        
    优化点:
        1. 增强错误处理和日志记录
        2. 改进流线生成算法稳定性
        3. 优化可视化效果
        4. 增加参数控制灵活性
        5. 提升代码可维护性
    """
    # 参数验证
    if not 3 <= streamline_density <= 10:
        streamline_density = 5
    if not 10 <= max_streamlines <= 100:
        max_streamlines = 50
    
    print("=" * 60)
    print("开始创建outlet子区域流线可视化（优化版）...")
    print(f"输出目录: {output_dir}")
    print(f"流线密度: {streamline_density}")
    print(f"最大流线数: {max_streamlines}")
    print("=" * 60)

    try:
        # 创建输出目录（增强错误处理）
        os.makedirs(output_dir, exist_ok=True)
        if not os.access(output_dir, os.W_OK):
            raise PermissionError(f"无法写入输出目录: {output_dir}")
    except Exception as e:
        print(f"错误：创建输出目录失败 - {str(e)}")
        return

    # 建立区域映射（使用更安全的方式）
    region_map = {}
    try:
        for region in regions:
            if not isinstance(region, dict):
                print(f"警告：跳过无效区域数据（非字典类型）")
                continue
            if "name" not in region:
                print(f"警告：跳过无名区域")
                continue
            region_map[region["name"]] = region
    except Exception as e:
        print(f"错误：处理区域数据失败 - {str(e)}")
        return

    # 检查必要的outlet相关区域是否存在（更详细的检查）
    required_regions = {
        "Subdomain outlet": "三维流体区域",
        "latooutlet Side 2": "入口平面",
        "outlettoo": "出口平面", 
        "outlet Default": "管道表面"
    }
    
    missing_regions = []
    for req, desc in required_regions.items():
        if req not in region_map:
            missing_regions.append((req, desc))
            
    if missing_regions:
        print("\n警告：缺失以下关键区域:")
        for req, desc in missing_regions:
            print(f"  - {req} ({desc})")
        
        print("\n可用区域:")
        for name in sorted(region_map.keys()):
            print(f"  - {name}")
            
        print("\n建议：请确保输入数据包含所有必需区域")
        return

    # 合并outlet相关区域的数据（更健壮的合并方式）
    outlet_regions = list(required_regions.keys())
    all_points = []
    all_data = []
    all_faces = []
    
    print("\n正在合并区域数据...")
    for region_name in outlet_regions:
        region = region_map[region_name]
        try:
            if "points" not in region or len(region["points"]) == 0:
                print(f"警告：区域 '{region_name}' 无点数据")
                continue
                
            if "data" not in region:
                print(f"警告：区域 '{region_name}' 无数据")
                continue
                
            all_points.append(region["points"])
            all_data.append(region["data"])
            
            # 处理面数据（更安全的方式）
            if "faces" in region and len(region["faces"]) > 0:
                valid_faces = [f for f in region["faces"] if len(f) >= 3]
                if valid_faces:
                    all_faces.extend(valid_faces)
                else:
                    print(f"警告：区域 '{region_name}' 无有效面数据")
                    
            print(f"  - 已添加 {region_name}: {len(region['points'])} 点, {len(region.get('faces', []))} 面")
            
        except Exception as e:
            print(f"错误：处理区域 '{region_name}' 失败 - {str(e)}")
            continue

    # 检查合并后的数据有效性
    if not all_points:
        print("错误：无有效点数据")
        return
        
    if not all_data:
        print("错误：无有效数据")
        return

    # 合并数据（增强错误处理）
    try:
        global_points = np.vstack(all_points)
        global_df = pd.concat(all_data, ignore_index=True)
        print(f"\n合并后的数据统计:")
        print(f"  - 总点数: {len(global_points)}")
        print(f"  - 总面数: {len(all_faces)}")
        print(f"  - 数据列: {list(global_df.columns)}")
    except Exception as e:
        print(f"错误：合并数据失败 - {str(e)}")
        return

    # 创建VTK非结构化网格（重构为单独函数）
    def create_vtk_grid(points, faces, data_df):
        """创建并配置VTK非结构化网格"""
        grid = vtk.vtkUnstructuredGrid()
        
        # 添加点
        points_vtk = vtk.vtkPoints()
        points_vtk.SetDataTypeToFloat()
        for point in points:
            points_vtk.InsertNextPoint(point[0], point[1], point[2])
        grid.SetPoints(points_vtk)

        # 添加单元（面）
        for face in faces:
            try:
                if len(face) == 3:
                    cell = vtk.vtkTriangle()
                elif len(face) == 4:
                    cell = vtk.vtkQuad()
                else:
                    cell = vtk.vtkPolygon()
                    cell.GetPointIds().SetNumberOfIds(len(face))

                for i, point_id in enumerate(face):
                    cell.GetPointIds().SetId(i, point_id)
                grid.InsertNextCell(cell.GetCellType(), cell.GetPointIds())
            except Exception as e:
                continue

        # 添加速度矢量数据
        velocity_cols = ['Velocity u [ m s^-1 ]', 'Velocity v [ m s^-1 ]',
                        'Velocity w [ m s^-1 ]']
        if all(col in data_df.columns for col in velocity_cols):
            velocity = data_df[velocity_cols].values.astype(np.float32)
            velocity_array = numpy_support.numpy_to_vtk(velocity)
            velocity_array.SetName("Velocity")
            velocity_array.SetNumberOfComponents(3)
            grid.GetPointData().SetVectors(velocity_array)
            grid.GetPointData().SetActiveVectors("Velocity")
            print("  - 已添加速度矢量数据")
        else:
            raise ValueError("缺失速度数据列")

        # 添加压强数据（如果存在）
        pressure_col = 'Pressure [ Pa ]'
        if pressure_col in data_df.columns:
            pressure = data_df[pressure_col].values.astype(np.float32)
            pressure_array = numpy_support.numpy_to_vtk(pressure)
            pressure_array.SetName("Pressure")
            grid.GetPointData().SetScalars(pressure_array)
            print("  - 已添加压强标量数据")

        # 计算速度大小
        velocity_magnitude = np.sqrt(np.sum(velocity**2, axis=1)).astype(np.float32)
        magnitude_array = numpy_support.numpy_to_vtk(velocity_magnitude)
        magnitude_array.SetName("VelocityMagnitude")
        grid.GetPointData().AddArray(magnitude_array)
        grid.GetPointData().SetActiveScalars("VelocityMagnitude")
        
        print(f"  - 速度范围: {velocity_magnitude.min():.3f} - {velocity_magnitude.max():.3f} m/s")
        
        return grid

    try:
        grid = create_vtk_grid(global_points, all_faces, global_df)
        print(f"\n网格信息:")
        print(f"  - 点数: {grid.GetNumberOfPoints()}")
        print(f"  - 单元数: {grid.GetNumberOfCells()}")
    except Exception as e:
        print(f"错误：创建网格失败 - {str(e)}")
        return

    # 创建渲染器
    renderer = vtk.vtkRenderer()
    renderer.SetBackground(0.05, 0.1, 0.2)
    renderer.SetBackground2(0.2, 0.3, 0.4)
    renderer.SetGradientBackground(True)

    # ================= 流线生成（优化版） =================
    print("\n开始流线生成...")
    
    # 获取入口平面数据
    inlet_region = region_map["latooutlet Side 2"]
    inlet_points = inlet_region["points"]
    
    # 计算入口平面边界和中心
    min_coords = inlet_points.min(axis=0)
    max_coords = inlet_points.max(axis=0)
    center = inlet_points.mean(axis=0)
    dimensions = max_coords - min_coords
    
    print(f"入口平面信息:")
    print(f"  - 点数: {len(inlet_points)}")
    print(f"  - 边界: X[{min_coords[0]:.3f}, {max_coords[0]:.3f}], "
          f"Y[{min_coords[1]:.3f}, {max_coords[1]:.3f}], "
          f"Z[{min_coords[2]:.3f}, {max_coords[2]:.3f}]")
    print(f"  - 中心点: ({center[0]:.6f}, {center[1]:.6f}, {center[2]:.6f})")

    # 创建KDTree用于快速查找（优化搜索参数）
    tree = cKDTree(global_points)
    velocity_data = global_df[['Velocity u [ m s^-1 ]',
                             'Velocity v [ m s^-1 ]',
                             'Velocity w [ m s^-1 ]']].values

    def get_interpolated_velocity(point, k=5, smoothing=0.1):
        """改进的速度插值函数"""
        distances, indices = tree.query(point, k=k)
        
        # 处理可能的无效距离
        valid_mask = ~np.isinf(distances)
        if not np.any(valid_mask):
            return np.zeros(3)
            
        distances = distances[valid_mask]
        indices = indices[valid_mask]
        
        # 避免除零错误
        distances = np.maximum(distances, 1e-12)
        
        # 使用高斯核函数加权
        weights = np.exp(-(distances**2) / (2 * smoothing**2))
        weights /= weights.sum()
        
        # 插值速度
        velocity = np.zeros(3)
        for i, idx in enumerate(indices):
            velocity += weights[i] * velocity_data[idx]
            
        # 速度限制和归一化
        velocity_norm = np.linalg.norm(velocity)
        if velocity_norm > 0:
            max_velocity = np.percentile(np.linalg.norm(velocity_data, axis=1), 95)
            if velocity_norm > max_velocity:
                velocity = velocity * (max_velocity / velocity_norm)
                
        return velocity

    # 流线追踪参数
    step_size = 0.002
    max_steps = 2000
    min_streamline_length = 10
    
    # 在入口平面创建种子点（优化分布）
    def generate_seed_points(density):
        """生成均匀分布的种子点"""
        # 确定主要变化的两个维度
        coord_ranges = max_coords - min_coords
        sorted_dims = np.argsort(coord_ranges)
        dim1, dim2 = sorted_dims[1], sorted_dims[2]
        
        # 生成网格点
        x_vals = np.linspace(min_coords[dim1], max_coords[dim1], density)
        y_vals = np.linspace(min_coords[dim2], max_coords[dim2], density)
        
        seeds = []
        for x in x_vals:
            for y in y_vals:
                point = center.copy()
                point[dim1] = x
                point[dim2] = y
                seeds.append(point)
                
        return np.array(seeds)

    seed_points = generate_seed_points(streamline_density)
    print(f"\n已生成 {len(seed_points)} 个种子点")

    # 流线追踪主循环（优化稳定性）
    streamlines = vtk.vtkPolyData()
    streamline_points = vtk.vtkPoints()
    streamline_cells = vtk.vtkCellArray()
    
    streamline_count = 0
    total_points = 0
    
    for seed in seed_points[:max_streamlines]:
        if streamline_count >= max_streamlines:
            break
            
        current_point = seed.copy()
        points_in_line = [current_point.copy()]
        
        # 正向追踪
        for step in range(max_steps // 2):
            velocity = get_interpolated_velocity(current_point)
            if np.linalg.norm(velocity) < 1e-6:
                break
                
            next_point = current_point + step_size * velocity
            points_in_line.append(next_point.copy())
            current_point = next_point
            
        # 反向追踪（从种子点开始）
        current_point = seed.copy()
        for step in range(max_steps // 2):
            velocity = get_interpolated_velocity(current_point)
            if np.linalg.norm(velocity) < 1e-6:
                break
                
            next_point = current_point - step_size * velocity
            points_in_line.insert(0, next_point.copy())
            current_point = next_point
            
        # 检查流线有效性
        if len(points_in_line) >= min_streamline_length:
            # 添加到VTK数据结构
            line_start = total_points
            for point in points_in_line:
                streamline_points.InsertNextPoint(point)
                total_points += 1
                
            line = vtk.vtkPolyLine()
            line.GetPointIds().SetNumberOfIds(len(points_in_line))
            for i in range(len(points_in_line)):
                line.GetPointIds().SetId(i, line_start + i)
                
            streamline_cells.InsertNextCell(line)
            streamline_count += 1
            
            print(f"生成流线 {streamline_count}: {len(points_in_line)} 点", end='\r')

    streamlines.SetPoints(streamline_points)
    streamlines.SetLines(streamline_cells)
    
    print(f"\n成功生成 {streamline_count} 条流线，共 {total_points} 个点")

    # 为流线添加颜色（基于速度大小）
    colors = vtk.vtkFloatArray()
    colors.SetNumberOfComponents(1)
    colors.SetName("VelocityMagnitude")
    
    velocity_magnitudes = []
    for i in range(streamline_points.GetNumberOfPoints()):
        point = streamline_points.GetPoint(i)
        velocity = get_interpolated_velocity(point)
        magnitude = np.linalg.norm(velocity)
        velocity_magnitudes.append(magnitude)
        colors.InsertNextValue(magnitude)
    
    streamlines.GetPointData().SetScalars(colors)
    
    # 创建流线mapper和actor
    stream_mapper = vtk.vtkPolyDataMapper()
    stream_mapper.SetInputData(streamlines)
    stream_mapper.SetScalarModeToUsePointData()
    stream_mapper.SelectColorArray("VelocityMagnitude")
    
    # 配置颜色映射
    min_mag, max_mag = min(velocity_magnitudes), max(velocity_magnitudes)
    stream_mapper.SetScalarRange(min_mag, max_mag)
    
    lut = vtk.vtkLookupTable()
    lut.SetNumberOfTableValues(256)
    lut.SetHueRange(0.667, 0.0)  # 蓝到红
    lut.SetSaturationRange(1.0, 1.0)
    lut.SetValueRange(1.0, 1.0)
    lut.Build()
    stream_mapper.SetLookupTable(lut)
    
    stream_actor = vtk.vtkActor()
    stream_actor.SetMapper(stream_mapper)
    stream_actor.GetProperty().SetLineWidth(3)
    stream_actor.GetProperty().SetOpacity(0.9)
    renderer.AddActor(stream_actor)

    # 添加颜色条
    scalar_bar = vtk.vtkScalarBarActor()
    scalar_bar.SetLookupTable(lut)
    scalar_bar.SetTitle("速度大小 (m/s)")
    scalar_bar.SetNumberOfLabels(5)
    scalar_bar.SetPosition(0.85, 0.1)
    scalar_bar.SetWidth(0.1)
    scalar_bar.SetHeight(0.7)
    renderer.AddActor2D(scalar_bar)

    # ================= 简化几何可视化 =================
    def create_point_cloud_actor(points, color, point_size=3, opacity=0.8):
        """创建点云可视化actor"""
        vtk_points = vtk.vtkPoints()
        for point in points:
            vtk_points.InsertNextPoint(point)
            
        poly = vtk.vtkPolyData()
        poly.SetPoints(vtk_points)
        
        verts = vtk.vtkCellArray()
        for i in range(vtk_points.GetNumberOfPoints()):
            verts.InsertNextCell(1)
            verts.InsertCellPoint(i)
        poly.SetVerts(verts)
        
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputData(poly)
        
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        actor.GetProperty().SetColor(color)
        actor.GetProperty().SetPointSize(point_size)
        actor.GetProperty().SetOpacity(opacity)
        
        return actor

    # 1. 入口平面
    inlet_actor = create_point_cloud_actor(
        inlet_region["points"], 
        (0, 0.8, 0.2),  # 亮绿色
        4, 0.8
    )
    renderer.AddActor(inlet_actor)

    # 2. 出口平面
    if "outlettoo" in region_map:
        outlet_points = region_map["outlettoo"]["points"]
        outlet_actor = create_point_cloud_actor(
            outlet_points[::2],  # 降采样
            (0.2, 0.4, 1.0),    # 亮蓝色
            3, 0.7
        )
        renderer.AddActor(outlet_actor)

    # 3. 管道表面
    if "outlet Default" in region_map:
        pipe_points = region_map["outlet Default"]["points"]
        pipe_actor = create_point_cloud_actor(
            pipe_points[::10],   # 降采样
            (0.6, 0.6, 0.8),    # 淡紫色
            2, 0.4
        )
        renderer.AddActor(pipe_actor)

    # ================= 保存结果 =================
    print("\n保存结果...")
    
    # 1. 保存网格数据
    grid_writer = vtk.vtkUnstructuredGridWriter()
    grid_writer.SetFileName(os.path.join(output_dir, "outlet_grid.vtk"))
    grid_writer.SetInputData(grid)
    grid_writer.Write()
    
    # 2. 保存流线数据
    stream_writer = vtk.vtkPolyDataWriter()
    stream_writer.SetFileName(os.path.join(output_dir, "outlet_streamlines.vtk"))
    stream_writer.SetInputData(streamlines)
    stream_writer.Write()
    
    # 3. 保存截图
    window_to_image = vtk.vtkWindowToImageFilter()
    window_to_image.SetInput(renderer.GetRenderWindow())
    window_to_image.Update()
    
    png_writer = vtk.vtkPNGWriter()
    png_writer.SetFileName(os.path.join(output_dir, "outlet_visualization.png"))
    png_writer.SetInputConnection(window_to_image.GetOutputPort())
    png_writer.Write()
    
    print("=" * 60)
    print("Outlet子区域流线可视化完成！")
    print(f"结果保存在: {output_dir}/")
    print("- outlet_grid.vtk (网格数据)")
    print("- outlet_streamlines.vtk (流线数据)")
    print("- outlet_visualization.png (可视化截图)")
    print("=" * 60)

    # 启动交互式可视化
    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    render_window.SetSize(1200, 900)
    render_window.SetWindowName("Outlet流线可视化 - 优化版")

    interactor = vtk.vtkRenderWindowInteractor()
    interactor.SetRenderWindow(render_window)
    
    # 添加键盘快捷键说明
    def key_pressed_callback(obj, event):
        key = obj.GetKeySym()
        if key == "h":
            print("\n交互快捷键:")
            print("  h - 显示帮助")
            print("  r - 重置视图")
            print("  s - 保存当前视图截图")
            print("  q - 退出")
        elif key == "r":
            renderer.ResetCamera()
            render_window.Render()
        elif key == "s":
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = os.path.join(output_dir, f"screenshot_{timestamp}.png")
            window_to_image.Modified()
            png_writer.SetFileName(screenshot_path)
            png_writer.Write()
            print(f"已保存截图: {screenshot_path}")
        elif key == "q":
            render_window.Finalize()
            interactor.TerminateApp()

    interactor.AddObserver("KeyPressEvent", key_pressed_callback)
    
    print("\n进入交互模式...")
    print("按 'h' 查看帮助，'q' 退出")
    
    renderer.ResetCamera()
    camera = renderer.GetActiveCamera()
    camera.Elevation(20)
    camera.Azimuth(45)
    camera.Zoom(1.2)
    
    render_window.Render()
    interactor.Start()

def main():
    """主执行函数"""
    # 加载和处理数据
    cache_file = os.path.join(os.getcwd(), "cache.pkl")
    if os.path.exists(cache_file):
        with open(cache_file, "rb") as f:
            regions = pickle.load(f)
    else:
        regions = load_and_process_data("steady_results.csv")
        with open(cache_file, "wb") as f:
            pickle.dump(regions, f)

    print([it['name'] for it in regions])

    # 只渲染outlet子区域流线： 三维区域是Subdomain outlet,
    # 入口平面是 latooutlet Side 2, 出口平面是 outlettoo,
    # 区域管道表面是outlet Default
    create_outlet_streamlines(regions)


if __name__ == "__main__":
    main()