import pickle
import numpy as np
import pandas as pd
from tqdm import tqdm
import vtk
from vtk.util import numpy_support
from typing import List, Dict, Any
import os
import re
from scipy.spatial import cKDTree


def load_and_process_data(filename: str) -> List[Dict[str, Any]]:
    """
    更稳健的文件加载方法，完全按照原始格式解析
    """
    with open(filename, 'r') as f:
        content = f.read()

    # 使用正则表达式精确分割区块
    blocks = re.split(r'\[Name\]\s*', content)[1:]

    regions = []
    offset = 0
    for block in tqdm(blocks, desc="处理区域数据", unit="region"):
        # 使用更安全的方式分割数据段
        data_parts = re.split(r'\[Data\]\s*', block, maxsplit=1)
        if len(data_parts) < 2:
            continue

        name = data_parts[0].strip()
        remaining = data_parts[1]

        # 分割面和数据
        face_parts = re.split(r'\[Faces\]\s*', remaining, maxsplit=1)
        if len(face_parts) < 2:
            continue

        data_section = face_parts[0]
        faces_section = face_parts[1]

        # 处理数据部分
        data_lines = [line.strip() for line in data_section.split('\n')
                      if line.strip()]
        header = [h.strip() for h in data_lines[0].split(',')]

        data = np.array([list(map(float, line.split(',')))
                         for line in data_lines[1:]])
        df = pd.DataFrame(data, columns=header)

        # 处理面数据
        faces = []
        ori_faces = []
        for line in faces_section.split('\n'):
            line = line.strip()
            if line:
                try:
                    ori_face = list(map(int, line.split(',')))
                    face = [(it + offset) for it in ori_face]
                    ori_faces.append(ori_face)
                    faces.append(face)
                except (ValueError, IndexError):
                    continue
        offset += len(data)
        regions.append({
            "name": name,
            "data": df,
            "faces": faces,
            "ori_faces": ori_faces,
            "points": df[['X [ m ]', 'Y [ m ]', 'Z [ m ]']].values.astype(
                np.float32)
        })
    print("数据加载处理完成，共处理了", len(regions), "个区域")
    return regions


def create_outlet_streamlines(regions: List[Dict[str, Any]],
                              output_dir: str = "outlet_streamlines"):
    """
    创建outlet子区域的流线可视化
    - 三维区域：Subdomain outlet
    - 入口平面：latooutlet Side 2
    - 出口平面：outlettoo
    - 区域管道表面：outlet Default
    """
    print("正在创建outlet子区域流线可视化...")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 建立区域映射
    region_map = {}
    for region in regions:
        region_map[region["name"]] = region

    # 检查必要的outlet相关区域是否存在
    required_regions = ["Subdomain outlet", "latooutlet Side 2",
                        "outlettoo", "outlet Default"]
    missing_regions = []
    for req in required_regions:
        if req not in region_map:
            missing_regions.append(req)

    if missing_regions:
        print(f"警告：缺失以下outlet相关区域: {missing_regions}")
        print(f"可用区域: {list(region_map.keys())}")
        return

    # 合并outlet相关区域的数据用于流线追踪
    outlet_regions = ["Subdomain outlet", "latooutlet Side 2",
                      "outlettoo", "outlet Default"]
    all_points = []
    all_data = []
    all_faces = []

    for region_name in outlet_regions:
        if region_name in region_map:
            region = region_map[region_name]
            all_points.append(region["points"])
            all_data.append(region["data"])
            all_faces.extend(region["faces"])

    # 合并数据
    global_points = np.vstack(all_points)
    global_df = pd.concat(all_data, ignore_index=True)

    print(f"合并后的outlet区域数据点数: {len(global_points)}")

    # 创建VTK非结构化网格
    grid = vtk.vtkUnstructuredGrid()

    # 添加点
    points_vtk = vtk.vtkPoints()
    for point in global_points:
        points_vtk.InsertNextPoint(point[0], point[1], point[2])
    grid.SetPoints(points_vtk)

    # 添加单元（面）
    for face in all_faces:
        if len(face) >= 3:  # 确保是有效的面
            if len(face) == 3:
                cell = vtk.vtkTriangle()
            elif len(face) == 4:
                cell = vtk.vtkQuad()
            else:
                cell = vtk.vtkPolygon()
                cell.GetPointIds().SetNumberOfIds(len(face))

            for i, point_id in enumerate(face):
                cell.GetPointIds().SetId(i, point_id)
            grid.InsertNextCell(cell.GetCellType(), cell.GetPointIds())

    # 添加速度矢量数据
    velocity_cols = ['Velocity u [ m s^-1 ]', 'Velocity v [ m s^-1 ]',
                     'Velocity w [ m s^-1 ]']
    if all(col in global_df.columns for col in velocity_cols):
        velocity = global_df[velocity_cols].values.astype(np.float32)
        velocity_array = numpy_support.numpy_to_vtk(velocity)
        velocity_array.SetName("Velocity")
        velocity_array.SetNumberOfComponents(3)
        grid.GetPointData().SetVectors(velocity_array)
        grid.GetPointData().SetActiveVectors("Velocity")
        print("已添加速度矢量数据")
    else:
        print("警告：未找到速度数据列")
        return

    # 添加压强数据（如果存在）
    pressure_col = 'Pressure [ Pa ]'
    if pressure_col in global_df.columns:
        pressure = global_df[pressure_col].values.astype(np.float32)
        pressure_array = numpy_support.numpy_to_vtk(pressure)
        pressure_array.SetName("Pressure")
        grid.GetPointData().SetScalars(pressure_array)
        print("已添加压强标量数据")

    # 计算速度大小（magnitude）
    velocity_magnitude = np.sqrt(np.sum(velocity**2, axis=1)).astype(np.float32)
    magnitude_array = numpy_support.numpy_to_vtk(velocity_magnitude)
    magnitude_array.SetName("VelocityMagnitude")
    grid.GetPointData().AddArray(magnitude_array)
    grid.GetPointData().SetActiveScalars("VelocityMagnitude")

    print(f"速度大小范围: {velocity_magnitude.min():.3f} - {velocity_magnitude.max():.3f} m/s")

    print(f"网格点数: {grid.GetNumberOfPoints()}")
    print(f"网格单元数: {grid.GetNumberOfCells()}")

    # 创建渲染器
    renderer = vtk.vtkRenderer()

    # ================= 创建流线 =================
    # 使用入口平面 "latooutlet Side 2" 进行均匀采样
    inlet_region = region_map["latooutlet Side 2"]
    inlet_points = inlet_region["points"]

    print(f"入口平面 'latooutlet Side 2' 点数: {len(inlet_points)}")

    # 计算入口平面的边界
    min_coords = inlet_points.min(axis=0)
    max_coords = inlet_points.max(axis=0)

    print(f"入口平面边界: X[{min_coords[0]:.3f}, {max_coords[0]:.3f}], "
          f"Y[{min_coords[1]:.3f}, {max_coords[1]:.3f}], "
          f"Z[{min_coords[2]:.3f}, {max_coords[2]:.3f}]")

    # 在入口平面创建均匀网格种子点
    grid_density = 3  # 3x3网格，最少种子点数量以提高稳定性
    seeds = vtk.vtkPolyData()
    seed_points = vtk.vtkPoints()

    # 确定主要变化的两个维度（排除变化最小的维度）
    coord_ranges = max_coords - min_coords
    sorted_dims = np.argsort(coord_ranges)

    # 使用变化最大的两个维度创建网格
    dim1, dim2 = sorted_dims[1], sorted_dims[2]  # 变化较大的两个维度
    fixed_dim = sorted_dims[0]  # 变化最小的维度

    for i in range(grid_density):
        for j in range(grid_density):
            # 在两个主要维度上均匀分布
            coord = min_coords.copy()
            coord[dim1] = (min_coords[dim1] +
                           (max_coords[dim1] - min_coords[dim1]) *
                           i / (grid_density - 1))
            coord[dim2] = (min_coords[dim2] +
                           (max_coords[dim2] - min_coords[dim2]) *
                           j / (grid_density - 1))
            coord[fixed_dim] = ((min_coords[fixed_dim] +
                                max_coords[fixed_dim]) / 2)  # 固定维度取中值

            seed_points.InsertNextPoint(coord[0], coord[1], coord[2])

    seeds.SetPoints(seed_points)

    # 创建顶点单元
    verts = vtk.vtkCellArray()
    for i in range(seed_points.GetNumberOfPoints()):
        verts.InsertNextCell(1)
        verts.InsertCellPoint(i)
    seeds.SetVerts(verts)

    print(f"创建了 {seed_points.GetNumberOfPoints()} 个种子点进行流线追踪")

    # 验证网格数据
    if grid.GetNumberOfPoints() == 0:
        print("错误：网格中没有点数据")
        return

    if grid.GetPointData().GetVectors() is None:
        print("错误：网格中没有速度矢量数据")
        return

    # 检查速度数据的有效性
    velocity_data = grid.GetPointData().GetVectors()
    velocity_range = velocity_data.GetRange(-1)  # 获取所有分量的范围
    print(f"速度数据范围: {velocity_range}")

    if velocity_range[1] == 0:
        print("警告：速度数据全为零，可能无法生成有效流线")

    # 使用简化的欧拉方法手动追踪流线，基于真实速度数据
    print("使用简化方法基于速度数据创建流线...")

    # 创建入口平面中心点
    center = inlet_points.mean(axis=0)
    print(f"入口平面中心: ({center[0]:.6f}, {center[1]:.6f}, {center[2]:.6f})")

    # 计算入口平面的主要变化维度
    min_coords = inlet_points.min(axis=0)
    max_coords = inlet_points.max(axis=0)
    coord_ranges = max_coords - min_coords
    sorted_dims = np.argsort(coord_ranges)

    # 使用变化最大的两个维度创建多条流线
    dim1, dim2 = sorted_dims[1], sorted_dims[2]  # 变化较大的两个维度

    # 创建KDTree用于快速查找最近邻点
    tree = cKDTree(global_points)

    # 获取速度数据
    velocity_data = global_df[['Velocity u [ m s^-1 ]',
                               'Velocity v [ m s^-1 ]',
                               'Velocity w [ m s^-1 ]']].values

    def get_velocity_at_point(point):
        """在给定点处插值速度"""
        # 找到最近的更多点进行更好的插值
        distances, indices = tree.query(point, k=8)

        # 避免除零错误
        distances = np.maximum(distances, 1e-12)

        # 使用反距离加权插值，距离越近权重越大
        weights = 1.0 / (distances ** 2)  # 平方反距离加权
        weights /= weights.sum()

        # 插值速度
        velocity = np.zeros(3)
        for i, idx in enumerate(indices):
            velocity += weights[i] * velocity_data[idx]

        # 对速度进行平滑处理
        velocity_magnitude = np.linalg.norm(velocity)
        if velocity_magnitude > 0:
            # 限制最大速度以避免数值不稳定
            max_velocity = 0.5  # 根据数据调整
            if velocity_magnitude > max_velocity:
                velocity = velocity * (max_velocity / velocity_magnitude)

        return velocity

    # 创建多条流线
    flow_direction = vtk.vtkPolyData()
    flow_points = vtk.vtkPoints()
    flow_lines = vtk.vtkCellArray()

    num_streamlines = 25  # 适中的流线数量
    max_steps = 10000  # 适中的最大步数
    step_size = 0.002  # 适中的积分步长

    point_id = 0

    # 在入口平面创建网格状的起始点
    grid_size = int(np.sqrt(num_streamlines))
    if grid_size * grid_size < num_streamlines:
        grid_size += 1

    streamline_count = 0
    for i in range(grid_size):
        for j in range(grid_size):
            if streamline_count >= num_streamlines:
                break

            # 计算起始点位置
            start_point = center.copy()
            if grid_size > 1:
                # 在入口平面上分布起始点，使用更小的范围
                offset1 = ((coord_ranges[dim1] * 0.6) *
                           (i / (grid_size - 1) - 0.5))
                offset2 = ((coord_ranges[dim2] * 0.6) *
                           (j / (grid_size - 1) - 0.5))
                start_point[dim1] += offset1
                start_point[dim2] += offset2

            # 放宽起始点的有效性检查
            distances_to_inlet = np.linalg.norm(inlet_points - start_point, axis=1)
            min_distance = distances_to_inlet.min()

            # 如果起始点离入口平面太远，跳过这条流线
            if min_distance > coord_ranges.max() * 0.3:  # 放宽限制
                continue

            # 使用简化的欧拉方法追踪流线
            current_point = start_point.copy()
            line_points = [current_point.copy()]

            for step in range(max_steps):
                try:
                    # 获取当前点的速度
                    velocity = get_velocity_at_point(current_point)
                    velocity_magnitude = np.linalg.norm(velocity)

                    # 检查速度是否有效
                    if velocity_magnitude < 1e-8:
                        break

                    # 使用固定步长，简化计算
                    # 欧拉积分：下一个点 = 当前点 + 步长 * 速度
                    next_point = current_point + step_size * velocity

                    # 检查是否超出边界
                    if (next_point[0] < global_points[:, 0].min() or
                        next_point[0] > global_points[:, 0].max() or
                        next_point[1] < global_points[:, 1].min() or
                        next_point[1] > global_points[:, 1].max() or
                        next_point[2] < global_points[:, 2].min() or
                        next_point[2] > global_points[:, 2].max()):
                        break

                    # 简化的循环检测
                    if len(line_points) > 20:
                        recent_points = np.array(line_points[-5:])
                        distances = np.linalg.norm(recent_points - next_point, axis=1)
                        if distances.min() < step_size * 0.1:
                            break

                    line_points.append(next_point.copy())
                    current_point = next_point

                except Exception as e:
                    print(f"流线 {streamline_count} 在步骤 {step} 处停止: {e}")
                    break

            # 如果流线有足够的点，添加到可视化中
            if len(line_points) > 5:  # 降低要求，确保能生成流线
                line_start_id = point_id
                for point in line_points:
                    flow_points.InsertNextPoint(point[0], point[1], point[2])
                    point_id += 1

                # 创建线段连接这条流线的点
                for k in range(len(line_points) - 1):
                    flow_lines.InsertNextCell(2)
                    flow_lines.InsertCellPoint(line_start_id + k)
                    flow_lines.InsertCellPoint(line_start_id + k + 1)

                print(f"流线 {streamline_count}: {len(line_points)} 个点")

            streamline_count += 1

        if streamline_count >= num_streamlines:
            break

    flow_direction.SetPoints(flow_points)
    flow_direction.SetLines(flow_lines)
    streamlines = flow_direction

    print(f"创建了 {streamline_count} 条基于速度数据的流线，总共 {point_id} 个点")

    print(f"生成的流线数: {streamlines.GetNumberOfCells()}")
    print(f"流线点数: {streamlines.GetNumberOfPoints()}")

    # 为流线添加颜色数据（基于位置的渐变色）
    colors = vtk.vtkUnsignedCharArray()
    colors.SetNumberOfComponents(3)
    colors.SetName("Colors")

    # 计算每个点的颜色（从红色到黄色的渐变）
    for i in range(streamlines.GetNumberOfPoints()):
        point = streamlines.GetPoint(i)
        # 基于速度计算颜色渐变


    streamlines.GetPointData().SetScalars(colors)

    # 创建流向指示线的mapper和actor
    stream_mapper = vtk.vtkPolyDataMapper()
    stream_mapper.SetInputData(streamlines)
    stream_mapper.SetScalarModeToUsePointData()
    stream_mapper.ScalarVisibilityOn()  # 显示颜色
    # stream_mapper.SelectColorArray("VelocityMagnitude")
    stream_mapper.SetScalarRange(0, 0.82)

    # 设置彩虹色谱查找表
    lut = vtk.vtkLookupTable()
    lut.SetNumberOfTableValues(256)
    lut.SetHueRange(0.667, 0.0)  # 从蓝色(0.667)到红色(0.0)的色相范围
    lut.SetSaturationRange(1.0, 1.0)  # 饱和度
    lut.SetValueRange(1.0, 1.0)  # 亮度
    lut.SetAlphaRange(1.0, 1.0)  # 透明度
    lut.Build()

    stream_mapper.SetLookupTable(lut)

    stream_actor = vtk.vtkActor()
    stream_actor.SetMapper(stream_mapper)
    stream_actor.GetProperty().SetLineWidth(3)  # 更粗的线条
    stream_actor.GetProperty().SetOpacity(0.9)

    renderer.AddActor(stream_actor)
    print("已添加多条彩色流向指示线")

    # ================= 添加简化的几何边界可视化 =================
    # 1. 入口平面 "latooutlet Side 2" - 只显示点云
    inlet_poly = vtk.vtkPolyData()
    inlet_points_vtk = vtk.vtkPoints()

    for point in inlet_region["points"]:
        inlet_points_vtk.InsertNextPoint(point[0], point[1], point[2])
    inlet_poly.SetPoints(inlet_points_vtk)

    # 添加颜色条（图例）
    scalar_bar = vtk.vtkScalarBarActor()
    scalar_bar.SetLookupTable(lut)
    scalar_bar.SetTitle("速度大小 (m/s)")
    scalar_bar.SetNumberOfLabels(5)
    scalar_bar.SetPosition(0.85, 0.1)
    scalar_bar.SetWidth(0.1)
    scalar_bar.SetHeight(0.8)
    renderer.AddActor2D(scalar_bar)

    # 创建顶点单元而不是面单元，避免复杂的面处理
    inlet_verts = vtk.vtkCellArray()
    for i in range(inlet_points_vtk.GetNumberOfPoints()):
        inlet_verts.InsertNextCell(1)
        inlet_verts.InsertCellPoint(i)
    inlet_poly.SetVerts(inlet_verts)

    inlet_mapper = vtk.vtkPolyDataMapper()
    inlet_mapper.SetInputData(inlet_poly)

    inlet_actor = vtk.vtkActor()
    inlet_actor.SetMapper(inlet_mapper)
    inlet_actor.GetProperty().SetColor(0, 0.8, 0.2)  # 亮绿色入口点云
    inlet_actor.GetProperty().SetPointSize(4)
    inlet_actor.GetProperty().SetRepresentationToPoints()
    inlet_actor.GetProperty().SetOpacity(0.8)

    renderer.AddActor(inlet_actor)
    print("已添加入口平面点云可视化")

    # 2. 出口平面 "outlettoo" - 简化为点云
    if "outlettoo" in region_map:
        outlet_region = region_map["outlettoo"]
        outlet_poly = vtk.vtkPolyData()
        outlet_points_vtk = vtk.vtkPoints()

        for point in outlet_region["points"]:
            outlet_points_vtk.InsertNextPoint(point[0], point[1], point[2])
        outlet_poly.SetPoints(outlet_points_vtk)

        # 创建顶点单元
        outlet_verts = vtk.vtkCellArray()
        for i in range(outlet_points_vtk.GetNumberOfPoints()):
            outlet_verts.InsertNextCell(1)
            outlet_verts.InsertCellPoint(i)
        outlet_poly.SetVerts(outlet_verts)

        outlet_mapper = vtk.vtkPolyDataMapper()
        outlet_mapper.SetInputData(outlet_poly)

        outlet_actor = vtk.vtkActor()
        outlet_actor.SetMapper(outlet_mapper)
        outlet_actor.GetProperty().SetColor(0.2, 0.4, 1)  # 亮蓝色出口点云
        outlet_actor.GetProperty().SetPointSize(4)
        outlet_actor.GetProperty().SetRepresentationToPoints()
        outlet_actor.GetProperty().SetOpacity(0.8)

        renderer.AddActor(outlet_actor)
        print("已添加出口平面点云可视化")

    # 3. 管道表面 "outlet Default" - 简化为点云
    if "outlet Default" in region_map:
        pipe_region = region_map["outlet Default"]
        pipe_poly = vtk.vtkPolyData()
        pipe_points_vtk = vtk.vtkPoints()

        # 只取部分点以减少渲染负担
        points_subset = pipe_region["points"][::10]  # 每10个点取1个
        for point in points_subset:
            pipe_points_vtk.InsertNextPoint(point[0], point[1], point[2])
        pipe_poly.SetPoints(pipe_points_vtk)

        # 创建顶点单元
        pipe_verts = vtk.vtkCellArray()
        for i in range(pipe_points_vtk.GetNumberOfPoints()):
            pipe_verts.InsertNextCell(1)
            pipe_verts.InsertCellPoint(i)
        pipe_poly.SetVerts(pipe_verts)

        pipe_mapper = vtk.vtkPolyDataMapper()
        pipe_mapper.SetInputData(pipe_poly)

        pipe_actor = vtk.vtkActor()
        pipe_actor.SetMapper(pipe_mapper)
        pipe_actor.GetProperty().SetColor(0.6, 0.6, 0.8)  # 淡紫灰色管道点云
        pipe_actor.GetProperty().SetPointSize(2)
        pipe_actor.GetProperty().SetRepresentationToPoints()
        pipe_actor.GetProperty().SetOpacity(0.4)

        renderer.AddActor(pipe_actor)
        print("已添加管道表面点云可视化")

    # ================= 设置渲染场景 =================
    # 设置渐变背景
    renderer.SetBackground(0.05, 0.1, 0.2)  # 深蓝色背景
    renderer.SetBackground2(0.2, 0.3, 0.4)  # 较亮的上部背景
    renderer.SetGradientBackground(True)

    # 添加坐标轴
    axes = vtk.vtkAxesActor()
    axes.SetTotalLength(0.02, 0.02, 0.02)  # 设置坐标轴长度
    axes.SetShaftType(0)  # 圆柱形轴
    axes.SetAxisLabels(True)

    renderer.AddActor(axes)

    # 重置相机并设置合适的视角
    renderer.ResetCamera()
    camera = renderer.GetActiveCamera()
    camera.Elevation(20)  # 仰角
    camera.Azimuth(45)    # 方位角
    camera.Zoom(1.2)      # 放大

    # 创建渲染窗口
    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    render_window.SetSize(1200, 900)
    render_window.SetWindowName("Outlet子区域流线可视化")

    # 创建交互器
    render_window_interactor = vtk.vtkRenderWindowInteractor()
    render_window_interactor.SetRenderWindow(render_window)

    # 保存VTK文件
    vtk_file = os.path.join(output_dir, "outlet_streamlines.vtk")
    writer = vtk.vtkUnstructuredGridWriter()
    writer.SetFileName(vtk_file)
    writer.SetInputData(grid)
    writer.Write()
    print(f"VTK文件已保存: {vtk_file}")

    # 保存流线数据
    streamline_file = os.path.join(output_dir, "outlet_streamlines_data.vtk")
    stream_writer = vtk.vtkPolyDataWriter()
    stream_writer.SetFileName(streamline_file)
    stream_writer.SetInputData(streamlines)
    stream_writer.Write()
    print(f"流线数据已保存: {streamline_file}")

    # 保存截图
    window_to_image = vtk.vtkWindowToImageFilter()
    window_to_image.SetInput(render_window)
    window_to_image.Update()

    png_writer = vtk.vtkPNGWriter()
    png_file = os.path.join(output_dir, "outlet_streamlines.png")
    png_writer.SetFileName(png_file)
    png_writer.SetInputConnection(window_to_image.GetOutputPort())
    png_writer.Write()
    print(f"截图已保存: {png_file}")

    print("=" * 60)
    print("Outlet子区域流线可视化完成！")
    print(f"结果保存在: {output_dir}/")
    print("包含文件:")
    print("- outlet_streamlines.vtk (网格数据)")
    print("- outlet_streamlines_data.vtk (流线数据)")
    print("- outlet_streamlines.png (可视化截图)")
    print("=" * 60)

    # 启动交互式可视化
    try:
        render_window.Render()
        print("渲染完成，启动交互式窗口...")
        render_window_interactor.Start()
    except Exception as e:
        print(f"可视化启动失败: {e}")
        print("但文件已成功保存")


def main():
    """主执行函数"""
    # 加载和处理数据
    cache_file = os.path.join(os.getcwd(), "cache.pkl")
    if os.path.exists(cache_file):
        with open(cache_file, "rb") as f:
            regions = pickle.load(f)
    else:
        regions = load_and_process_data("steady_results.csv")
        with open(cache_file, "wb") as f:
            pickle.dump(regions, f)

    print([it['name'] for it in regions])

    # 只渲染outlet子区域流线： 三维区域是Subdomain outlet,
    # 入口平面是 latooutlet Side 2, 出口平面是 outlettoo,
    # 区域管道表面是outlet Default
    create_outlet_streamlines(regions)


if __name__ == "__main__":
    main()